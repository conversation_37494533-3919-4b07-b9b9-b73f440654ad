# File Upload Components

This directory contains components for handling file uploads with progress tracking.

## Components

### FileList

A React component that displays a list of files with their upload status and progress.

#### Features

- **Upload Progress Display**: Shows real-time upload progress with percentage (e.g., "上传中 ... 45%")
- **Visual Progress Bar**: Animated progress bar that updates during upload
- **File Status Management**: Supports three states:
  - `success`: File uploaded successfully
  - `failed`: Upload failed (shows "上传失败")
  - `uploading`: Currently uploading (shows progress)
- **Interactive Actions**:
  - Click failed files to retry upload
  - Remove files with the X button
  - Start new uploads with the demo button

#### File Status Display

When a file is uploading:
- Status text shows: "上传中 ... X%" where X is the current percentage
- A blue progress bar visually represents the upload progress
- The progress updates every 200ms with realistic increments

#### Usage

```tsx
import { FileList } from "@cscs-agent/presets";

function App() {
  return (
    <div>
      <FileList />
    </div>
  );
}
```

#### File Item Interface

```typescript
interface FileItem {
  id: string;
  name: string;
  type: "word" | "markdown" | "text" | "json";
  size: string;
  status: "success" | "failed" | "uploading";
  progress?: number; // Upload progress percentage (0-100)
}
```

#### Demo Features

The component includes demo functionality:
- Sample files with different statuses
- Simulated upload progress with realistic timing
- "开始新上传 (演示)" button to trigger new uploads
- Automatic progress simulation for uploading files

#### Styling

The component uses Tailwind CSS classes with:
- Blue color scheme for upload progress (`text-blue-500`, `bg-blue-500`)
- Smooth transitions and animations
- Responsive design with proper spacing
- Visual feedback for different file states

## Implementation Details

### Progress Simulation

The `simulateUploadProgress` function:
- Updates progress in random increments (5-20% per update)
- Updates every 200ms for smooth animation
- Automatically completes at 100% and changes status to "success"
- Clears progress data when upload completes

### State Management

- Uses React hooks (`useState`, `useEffect`) for state management
- Maintains file list with proper immutable updates
- Handles concurrent uploads independently
- Preserves file order and IDs throughout the upload process

## Future Enhancements

Potential improvements could include:
- Real file upload integration with APIs
- Drag and drop functionality
- File type validation
- Upload cancellation
- Batch upload operations
- Error handling and retry mechanisms
