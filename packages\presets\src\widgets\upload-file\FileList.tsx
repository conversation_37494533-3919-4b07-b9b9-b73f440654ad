"use client";

import React, { useEffect, useState } from "react";

interface FileItem {
  id: string;
  name: string;
  type: "word" | "markdown" | "text" | "json";
  size: string;
  status: "success" | "failed" | "uploading";
  progress?: number; // Upload progress percentage (0-100)
}

const fileTypeIcons = {
  word: { icon: "W", bgColor: "bg-blue-500", textColor: "text-white" },
  markdown: { icon: "</>", bgColor: "bg-cyan-500", textColor: "text-white" },
  text: { icon: "≡", bgColor: "bg-gray-500", textColor: "text-white" },
  json: { icon: "</>", bgColor: "bg-cyan-500", textColor: "text-white" },
};

const FileList: React.FC = () => {
  const [showTooltip, setShowTooltip] = useState(true);
  const [files, setFiles] = useState<FileItem[]>([
    {
      id: "1",
      name: "Word文件名称新...",
      type: "word",
      size: "108 KB",
      status: "success",
    },
    {
      id: "2",
      name: "Markdown文件新...",
      type: "markdown",
      size: "md",
      status: "failed",
    },
    {
      id: "3",
      name: "文本文件名称新建...",
      type: "text",
      size: "108 KB",
      status: "success",
    },
    {
      id: "4",
      name: "JSON文件...",
      type: "json",
      size: "108 KB",
      status: "success",
    },
    {
      id: "5",
      name: "上传中的文件...",
      type: "text",
      size: "256 KB",
      status: "uploading",
      progress: 45,
    },
  ]);

  const removeFile = (id: string) => {
    setFiles(files.filter((file) => file.id !== id));
  };

  const startNewUpload = () => {
    const newFile: FileItem = {
      id: Date.now().toString(),
      name: "新上传文件.pdf",
      type: "text",
      size: "512 KB",
      status: "uploading",
      progress: 0,
    };
    setFiles((prevFiles) => [...prevFiles, newFile]);
    simulateUploadProgress(newFile.id);
  };

  const simulateUploadProgress = (id: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15 + 5; // Random increment between 5-20%
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        // Complete upload
        setTimeout(() => {
          setFiles((prevFiles) =>
            prevFiles.map((file) =>
              file.id === id ? { ...file, status: "success" as const, progress: undefined } : file,
            ),
          );
        }, 200);
      }

      setFiles((prevFiles) =>
        prevFiles.map((file) => (file.id === id ? { ...file, progress: Math.min(progress, 100) } : file)),
      );
    }, 200); // Update every 200ms
  };

  const retryUpload = (id: string) => {
    setFiles((prevFiles) =>
      prevFiles.map((file) => (file.id === id ? { ...file, status: "uploading" as const, progress: 0 } : file)),
    );
    simulateUploadProgress(id);
  };

  // Start demo upload progress for the uploading file
  useEffect(() => {
    const uploadingFile = files.find((file) => file.status === "uploading");
    if (uploadingFile) {
      simulateUploadProgress(uploadingFile.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount

  return (
    <div className="relative w-full max-w-4xl">
      {showTooltip && (
        <div className="top-0 left-1/2 z-10 absolute mb-2 -translate-x-1/2 -translate-y-full transform">
          <div className="bg-gray-800 px-3 py-2 rounded-lg font-medium text-white text-sm">
            点击重试
            <div className="top-full left-1/2 absolute border-t-4 border-t-gray-800 border-transparent border-r-4 border-l-4 w-0 h-0 -translate-x-1/2 transform"></div>
          </div>
        </div>
      )}

      {/* File Upload Container */}
      <div className="bg-white shadow-sm p-6 border border-gray-200 rounded-xl">
        <div className="flex justify-between items-center">
          <button className="flex justify-center items-center hover:bg-gray-100 rounded-full w-8 h-8 transition-colors">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          {/* File Items */}
          <div className="flex flex-1 justify-center items-center gap-4">
            {files.map((file) => {
              const iconConfig = fileTypeIcons[file.type];

              return (
                <div
                  key={file.id}
                  className="relative bg-gray-50 hover:bg-gray-100 p-4 rounded-lg w-48 transition-colors cursor-pointer"
                  onClick={() => (file.status === "failed" ? retryUpload(file.id) : undefined)}
                >
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(file.id);
                    }}
                    className="top-2 right-2 absolute flex justify-center items-center bg-gray-400 hover:bg-gray-500 rounded-full w-5 h-5 transition-colors"
                  >
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>

                  {/* File Icon and Info */}
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`w-10 h-10 ${iconConfig.bgColor} rounded-lg flex items-center justify-center`}>
                      <span className={`font-bold text-sm ${iconConfig.textColor}`}>{iconConfig.icon}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 text-sm truncate">{file.name}</div>
                      <div className="flex items-center gap-2 mt-1">
                        {file.status === "uploading" ? (
                          <span className="font-medium text-blue-500 text-xs">
                            上传中 ... {Math.round(file.progress || 0)}%
                          </span>
                        ) : (
                          <span className="text-gray-500 text-xs">{file.type === "markdown" ? "md" : file.size}</span>
                        )}
                        {file.status === "failed" && <span className="font-medium text-red-500 text-xs">上传失败</span>}
                      </div>
                    </div>
                  </div>

                  {/* Upload Progress Bar */}
                  {file.status === "uploading" && (
                    <div className="bg-gray-200 mb-2 rounded-full w-full h-1.5">
                      <div
                        className="bg-blue-500 rounded-full h-1.5 transition-all duration-300 ease-out"
                        style={{ width: `${file.progress || 0}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <button className="flex justify-center items-center hover:bg-gray-100 rounded-full w-8 h-8 transition-colors">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Demo Upload Button */}
      <div className="mt-4 text-center">
        <button
          onClick={startNewUpload}
          className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 mx-auto px-4 py-2 rounded-lg text-white transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
          开始新上传 (演示)
        </button>
      </div>

      <input type="file" />
    </div>
  );
};

export default FileList;
