/// <reference types="vite-plugin-svgr/client" />

import React, { CSSProperties, useMemo } from "react";

import AddPrompt from "./AddPrompt.svg?react";
import AddTemplate from "./AddTemplate.svg?react";
import ArrowUp from "./ArrowUp.svg?react";
import BackTop from "./BackTop.svg?react";
import Chat from "./Chat.svg?react";
import ChatFilled from "./ChatFilled.svg?react";
import Collapsed from "./Collapsed.svg?react";
import Copy from "./Copy.svg?react";
import CopyCreate from "./CopyCreate.svg?react";
import CreatePage from "./CreatePage.svg?react";
import Delete from "./Delete.svg?react";
import Edit from "./Edit.svg?react";
import EditPage from "./EditPage.svg?react";
import Expanded from "./Expanded.svg?react";
import Eye from "./Eye.svg?react";
import History from "./History.svg?react";
import Left from "./Left.svg?react";
import Loading from "./Loading.svg?react";
import Maximize from "./Maximize.svg?react";
import Minimize from "./Minimize.svg?react";
import NewChat from "./NewChat.svg?react";
import Refresh from "./Refresh.svg?react";
import Right from "./Right.svg?react";
import Save from "./Save.svg?react";
import Search from "./Search.svg?react";
import Send from "./Send.svg?react";
import SideBar from "./SideBar.svg?react";
import Stop from "./Stop.svg?react";
import Template from "./Template.svg?react";
import Tool from "./Tool.svg?react";
import Web from "./Web.svg?react";

const ICON_MAP: Record<string, React.FC> = {
  Web,
  Search,
  Save,
  Chat,
  ArrowUp,
  Tool,
  Stop,
  Minimize,
  Maximize,
  Copy,
  Send,
  SideBar,
  Template,
  CreatePage,
  EditPage,
  Expanded,
  Collapsed,
  Right,
  Loading,
  AddTemplate,
  CopyCreate,
  Edit,
  Delete,
  BackTop,
  Eye,
  Left,
  AddPrompt,
  History,
  Refresh,
  NewChat,
  ChatFilled,
};

export const Icon: React.FC<{
  icon: string;
  style?: CSSProperties;
  className?: string;
  onClick?: React.MouseEventHandler<HTMLElement>;
}> = (props) => {
  const { icon, className, ...restProps } = props ?? {};

  const Svg = useMemo(() => {
    if (!icon) return null;
    return ICON_MAP[icon];
  }, [icon]);

  return Svg ? (
    <div className={`cscs-agent-icon ${className ?? ""}`} style={props.style} {...restProps}>
      <Svg />
    </div>
  ) : null;
};
