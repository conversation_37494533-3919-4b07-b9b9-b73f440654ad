import { But<PERSON> } from "antd";
import React, { useEffect, useMemo, useRef, useState } from "react";

import { useCommandRunner, useSubscribeCommand } from "@/command";
import {
  useActiveAgentCode,
  useActiveAgentConfig,
  useActiveConversationId,
  useConversationPreState,
  useIsLoadingMessage,
} from "@/core";
import {
  BuildInCommand,
  IInsertSelectIntoSenderParams,
  IInsertTagIntoSenderParams,
  IInsertTextIntoSenderParams,
  IUpdateExtendSenderData,
  IUpdateExtendSenderParams,
  IUpdateSenderTextParams,
} from "@/types";
import { Icon } from "@cscs-agent/icons";

import Editor, { EditorRef } from "../editor/Editor";
import SenderFooterInside from "./FooterInside";
import SenderFooterOutside from "./FooterOutside";
import SenderHeaderInside from "./HeaderInside";
import SenderHeaderOutside from "./HeaderOutside";
import HeaderPanel from "./HeaderPanel";
import SendButton from "./SendButton";

interface SenderProps {
  isNewConversation?: boolean;
}

const Sender: React.FC<SenderProps> = (props) => {
  const { isNewConversation = false } = props;
  const [inputValue, setInputValue] = useState("");
  const [extendParams, setExtendParams] = useState({});
  const [extendData, setExtendData] = useState<Record<string, unknown> | null>(null);
  const runner = useCommandRunner();
  const [activeConversationId] = useActiveConversationId();
  const [isLoadingMessage] = useIsLoadingMessage();
  const editorRef = useRef<EditorRef>(null);
  const [activeAgentCode] = useActiveAgentCode();
  const [headerPanelOpen, setHeaderPanelOpen] = useState(false);
  const activeAgentConfig = useActiveAgentConfig();
  const { state: conversationPreState } = useConversationPreState();

  const disabled = useMemo(() => {
    const textValue = inputValue.replace(/\n/g, "") ?? "";
    return textValue.trim().replace("&#xFEFF;", "") === "";
  }, [inputValue]);

  useEffect(() => {
    setExtendParams({});
  }, [activeConversationId]);

  // 插入文字命令订阅，订阅到后，将文字插入到编辑器中。
  useSubscribeCommand(BuildInCommand.InsertTextIntoSender, (params) => {
    const { text } = params as IInsertTextIntoSenderParams;
    editorRef.current?.insertText(text);
  });

  // 插入标签命令订阅，订阅到后，将标签插入到编辑器中。
  useSubscribeCommand(BuildInCommand.InsertTagIntoSender, (params) => {
    const { content, rawValue, tooltips } = params as IInsertTagIntoSenderParams;
    const tagText = `<embedded-tag content="${content}" rawValue="${rawValue}" tooltips="${tooltips}"></embedded-tag>&#xFEFF;`;
    editorRef.current?.insertText(tagText);
  });

  // 插入选择器命令订阅，订阅到后，将选择器插入到编辑器中。
  useSubscribeCommand(BuildInCommand.InsertSelectIntoSender, (params) => {
    const { placeholder, options, defaultValue, tooltips, disabled } = params as IInsertSelectIntoSenderParams;
    const optionsStr = JSON.stringify(options).replace(/"/g, "&quot;");
    const selectText = `<embedded-select placeholder="${placeholder || "Please select..."}" options="${optionsStr}" defaultValue="${defaultValue || ""}" tooltips="${tooltips || ""}" disabled="${disabled || false}"></embedded-select>`;
    editorRef.current?.insertText(selectText);
  });

  // 清空编辑器
  useSubscribeCommand(BuildInCommand.ClearSender, () => {
    editorRef.current?.clear();
  });

  // 更新编辑器文本
  useSubscribeCommand(BuildInCommand.UpdateSenderText, (params: IUpdateSenderTextParams) => {
    const { text } = params;
    editorRef.current?.setText(text);
  });

  // 关闭头部面板命令订阅
  useSubscribeCommand(BuildInCommand.CloseSenderHeaderPanel, () => {
    setHeaderPanelOpen(false);
  });

  useSubscribeCommand(BuildInCommand.UpdateExtendSenderParams, (params: IUpdateExtendSenderParams) => {
    const { setValue } = params;
    if (setValue) {
      setExtendParams((prevValue) => setValue(prevValue));
    }
  });

  useSubscribeCommand(BuildInCommand.UpdateExtendSenderData, (params: IUpdateExtendSenderData) => {
    const { setValue } = params;
    if (setValue) {
      setExtendData((prevValue) => setValue(prevValue));
    }
  });

  // 路由变更时，清空编辑器，关闭头部面板
  useEffect(() => {
    editorRef.current?.clear();
    setHeaderPanelOpen(false);
    setInputValue("");
  }, [activeConversationId, activeAgentCode]);

  const onSubmit = () => {
    const message = editorRef.current?.getText() ?? "";
    setHeaderPanelOpen(false);
    setInputValue("");
    if (isNewConversation) {
      runner(BuildInCommand.SendMessage, {
        message,
        agentCode: activeAgentCode,
        isNewConversation: true,
        extendParams,
        extendData,
        conversationPreState,
      });
    } else {
      runner(BuildInCommand.SendMessage, {
        message,
        conversationId: activeConversationId,
        agentCode: activeAgentCode,
        extendParams,
        extendData,
      });
    }
    editorRef.current?.clear();
  };

  const cancel = () => {
    runner(BuildInCommand.CancelMessageGenerate);
  };

  const handleEnterPress = (event: KeyboardEvent) => {
    // 如果是Ctrl+Enter，允许换行，不提交
    if (event.ctrlKey) {
      return;
    }

    // 如果是普通Enter键，阻止默认行为并提交消息
    event.preventDefault();

    // 检查是否有内容可以提交
    const message = editorRef.current?.getText() ?? "";
    if (message.trim() !== "" && !isLoadingMessage) {
      onSubmit();
    }
  };

  const enableHeaderPanel = activeAgentConfig?.sender?.headerPanel?.enable ?? true;

  return (
    <>
      <SenderHeaderOutside />
      <HeaderPanel open={headerPanelOpen} onClose={() => setHeaderPanelOpen(false)} />
      <div className="ag:flex ag:items-stretch ag:bg-white ag:shadow-sm ag:p-4 ag:rounded-lg cscs-agent-sender">
        <div className="ag:flex-1">
          <div>
            <SenderHeaderInside />
          </div>
          <Editor onChange={setInputValue} onEnterPress={handleEnterPress} ref={editorRef} />
          <div className="ag:min-h-[32px]">
            <SenderFooterInside />
          </div>
        </div>
        <div className="ag:flex ag:flex-col ag:justify-between ag:items-end ag:ml-2 ag:w-[52px]">
          <div>
            <Button
              icon={
                headerPanelOpen ? (
                  <Icon icon="Minimize" className="ag:text-black-65" />
                ) : (
                  <Icon icon="Maximize" className="ag:text-black-65" />
                )
              }
              onClick={() => setHeaderPanelOpen(!headerPanelOpen)}
              type="text"
              size="small"
              hidden={!enableHeaderPanel}
            >
              <span className="ag:pl-1 ag:text-black-65">模板</span>
            </Button>
          </div>
          <SendButton onClick={() => onSubmit()} onCancel={cancel} isLoading={isLoadingMessage} disabled={disabled} />
        </div>
      </div>
      <SenderFooterOutside />
    </>
  );
};

export default Sender;
